<template>
  <div class="consultancy-dashboard">
    <!-- Header Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <h2>Consultancy Dashboard</h2>
          <div class="btn-group" role="group">
            <button
              type="button"
              class="btn"
              :class="activeTab === 'available' ? 'btn-primary' : 'btn-outline-primary'"
              @click="activeTab = 'available'"
            >
              Available Slots
            </button>
            <button
              type="button"
              class="btn"
              :class="activeTab === 'bookings' ? 'btn-primary' : 'btn-outline-primary'"
              @click="activeTab = 'bookings'"
            >
              My Bookings
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Summary Card -->
    <div class="row mb-4" v-if="consultancyUsage">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-8">
                <h5 class="card-title mb-2">Consultancy Usage</h5>
                <div class="progress mb-2" style="height: 8px;">
                  <div
                    class="progress-bar"
                    :class="usagePercentage > 80 ? 'bg-danger' : usagePercentage > 60 ? 'bg-warning' : 'bg-success'"
                    :style="{ width: usagePercentage + '%' }"
                  ></div>
                </div>
                <div class="d-flex justify-content-between">
                  <small class="text-muted">
                    Used: {{ usedHours }} hours
                  </small>
                  <small class="text-muted">
                    Remaining: {{ remainingHours }} hours
                  </small>
                </div>
              </div>
              <div class="col-md-4 text-end">
                <h3 class="mb-0">{{ remainingHours }}h</h3>
                <small class="text-muted">remaining</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Access Notice -->
    <div class="row mb-4" v-if="!consultancyUsage">
      <div class="col-12">
        <div class="alert alert-warning d-flex align-items-center">
          <i class="fa fa-exclamation-triangle me-2"></i>
          <div class="flex-grow-1">
            <strong>No consultancy access!</strong>
            Please upgrade your package to access consultancy services.
          </div>
          <button class="btn btn-warning btn-sm" @click="goToPackages">
            View Packages
          </button>
        </div>
      </div>
    </div>

    <!-- Upgrade Notice -->
    <div class="row mb-4" v-else-if="consultancyUsage.remaining_minutes <= 0">
      <div class="col-12">
        <div class="alert alert-warning d-flex align-items-center">
          <i class="fa fa-exclamation-triangle me-2"></i>
          <div class="flex-grow-1">
            <strong>No consultancy hours remaining!</strong>
            Upgrade your package to continue booking sessions.
          </div>
          <button class="btn btn-warning btn-sm" @click="goToPackages">
            Upgrade Package
          </button>
        </div>
      </div>
    </div>

    <!-- Filters (for available slots) -->
    <div class="row mb-4" v-if="activeTab === 'available' && consultancyUsage">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <h6 class="card-title">Filter Available Slots</h6>
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Type</label>
                <select v-model="filters.type" class="form-select">
                  <option value="">All Types</option>
                  <option value="online">Online</option>
                  <option value="in_person">In Person</option>
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">From Date</label>
                <input 
                  type="date" 
                  v-model="filters.date_from" 
                  class="form-control"
                  :min="new Date().toISOString().split('T')[0]"
                >
              </div>
              <div class="col-md-3">
                <label class="form-label">To Date</label>
                <input 
                  type="date" 
                  v-model="filters.date_to" 
                  class="form-control"
                  :min="filters.date_from || new Date().toISOString().split('T')[0]"
                >
              </div>
              <div class="col-md-3">
                <label class="form-label">Country</label>
                <select v-model="filters.country" class="form-select">
                  <option value="">All Countries</option>
                  <option v-for="country in countries" :key="country" :value="country">
                    {{ country }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Slots Tab -->
    <div v-if="activeTab === 'available' && consultancyUsage">
      <div class="row mb-3">
        <div class="col-12">
          <h4>Available Consultation Slots</h4>
        </div>
      </div>
      
      <div v-if="slotsLoading" class="text-center py-4">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
      
      <div v-else-if="availableSlots.length === 0" class="text-center py-4">
        <div class="alert alert-info">
          <i class="fa fa-info-circle me-2"></i>
          No available slots found. Please try adjusting your filters.
        </div>
      </div>
      
      <div v-else class="row">
        <div 
          v-for="slot in availableSlots" 
          :key="slot._id" 
          class="col-md-6 col-lg-4 mb-4"
        >
          <div class="card h-100">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-start mb-2">
                <span 
                  class="badge"
                  :class="slot.type === 'online' ? 'bg-primary' : 'bg-success'"
                >
                  {{ slot.type === 'online' ? 'Online' : 'In Person' }}
                </span>
                <small class="text-muted">
                  {{ slot.duration_minutes || 60 }} min
                </small>
              </div>
              
              <h6 class="card-title">{{ formatDate(slot.date) }}</h6>
              <p class="card-text">
                <i class="fa fa-clock me-1"></i>
                {{ formatSlotTime(slot) }}
              </p>
              
              <div v-if="slot.type === 'in_person'" class="mb-2">
                <small class="text-muted">
                  <i class="fa fa-map-marker-alt me-1"></i>
                  {{ slot.city || 'N/A' }}, {{ slot.country || 'N/A' }}
                </small>
              </div>
              
              <div class="mb-3">
                <small class="text-muted">
                  Available: {{ slot.available_capacity || slot.max_capacity || 1 }}/{{ slot.max_capacity || 1 }}
                </small>
              </div>
              
              <button
                class="btn btn-primary btn-sm w-100"
                @click="openBookingModal(slot)"
                :disabled="bookingLoading || (consultancyUsage && consultancyUsage.remaining_minutes <= 0)"
              >
                <span v-if="bookingLoading" class="spinner-border spinner-border-sm me-1"></span>
                Book Session
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- My Bookings Tab -->
    <div v-if="activeTab === 'bookings' && consultancyUsage">
      <div class="row mb-3">
        <div class="col-12">
          <h4>My Bookings</h4>
        </div>
      </div>
      
      <div v-if="bookingsLoading" class="text-center py-4">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
      
      <div v-else-if="myBookings.length === 0" class="text-center py-4">
        <div class="alert alert-info">
          <i class="fa fa-info-circle me-2"></i>
          You haven't booked any consultation sessions yet.
        </div>
      </div>
      
      <div v-else class="row">
        <div class="col-12">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Date & Time</th>
                  <th>Type</th>
                  <th>Duration</th>
                  <th>Status</th>
                  <th>Meeting Link</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="booking in myBookings" :key="booking._id">
                  <td>
                    <div>
                      <strong>{{ formatDate(booking.slot?.date || booking.date) }}</strong><br>
                      <small class="text-muted">
                        {{ formatBookingTime(booking) }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <span 
                      class="badge"
                      :class="(booking.slot?.type || booking.type) === 'online' ? 'bg-primary' : 'bg-success'"
                    >
                      {{ (booking.slot?.type || booking.type) === 'online' ? 'Online' : 'In Person' }}
                    </span>
                  </td>
                  <td>{{ booking.duration_minutes || 60 }} min</td>
                  <td>
                    <span 
                      class="badge"
                      :class="getStatusBadgeClass(booking.status)"
                    >
                      {{ booking.status ? booking.status.charAt(0).toUpperCase() + booking.status.slice(1) : 'Unknown' }}
                    </span>
                  </td>
                  <td>
                    <a 
                      v-if="booking.meeting_link && booking.status === 'confirmed'"
                      :href="booking.meeting_link"
                      target="_blank"
                      class="btn btn-sm btn-outline-primary"
                    >
                      <i class="fa fa-video me-1"></i>
                      Join
                    </a>
                    <span v-else class="text-muted">—</span>
                  </td>
                  <td>
                    <button
                      v-if="canCancelBooking(booking)"
                      class="btn btn-sm btn-outline-danger"
                      @click="cancelBookingWithConfirm(booking._id)"
                    >
                      Cancel
                    </button>
                    <span v-else class="text-muted">—</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Booking Modal -->
    <div 
      v-if="showBookingModal" 
      class="modal fade show d-block" 
      tabindex="-1"
      style="background: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Book Consultation Session</h5>
            <button type="button" class="btn-close" @click="closeBookingModal"></button>
          </div>
          <div class="modal-body" v-if="selectedSlot">
            <div class="mb-3">
              <h6>Session Details</h6>
              <p class="mb-1"><strong>Date:</strong> {{ formatDate(selectedSlot.date) }}</p>
              <p class="mb-1"><strong>Time:</strong> {{ formatSlotTime(selectedSlot) }}</p>
              <p class="mb-1"><strong>Type:</strong> {{ selectedSlot.type === 'online' ? 'Online' : 'In Person' }}</p>
              <p class="mb-1"><strong>Duration:</strong> {{ selectedSlot.duration_minutes || 60 }} minutes</p>
              <p v-if="selectedSlot.type === 'in_person'" class="mb-0">
                <strong>Location:</strong> {{ selectedSlot.city || 'N/A' }}, {{ selectedSlot.country || 'N/A' }}
              </p>
            </div>
            
            <div class="mb-3">
              <label for="bookingNotes" class="form-label">Notes (Optional)</label>
              <textarea 
                id="bookingNotes"
                v-model="bookingNotes" 
                class="form-control" 
                rows="3"
                placeholder="Any specific topics or questions you'd like to discuss..."
                maxlength="500"
              ></textarea>
              <div class="form-text">{{ bookingNotes.length }}/500 characters</div>
            </div>
            
            <div class="alert alert-info">
              <small>
                <i class="fa fa-info-circle me-1"></i>
                This will use {{ selectedSlot.duration_minutes || 60 }} minutes from your consultancy allowance.
                Remaining: {{ remainingHours }} hours.
              </small>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeBookingModal">
              Cancel
            </button>
            <button 
              type="button" 
              class="btn btn-primary"
              @click="confirmBooking"
              :disabled="bookingLoading"
            >
              <span v-if="bookingLoading" class="spinner-border spinner-border-sm me-1"></span>
              Confirm Booking
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ConsultancyService from '@/services/ConsultancyService';

export default {
  name: 'UserConsultancyDashboard',
  data() {
    return {
      activeTab: 'available',
      availableSlots: [],
      myBookings: [],
      consultancyUsage: null,
      slotsLoading: false,
      bookingsLoading: false,
      bookingLoading: false,
      showBookingModal: false,
      
      filters: {
        type: '',
        date_from: '',
        date_to: '',
        country: ''
      },

      countries: [
        'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 
        'France', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Switzerland'
      ],

      selectedSlot: null,
      bookingNotes: ''
    };
  },

  computed: {
    usedHours() {
      return this.consultancyUsage ? 
        Math.round(this.consultancyUsage.used_minutes / 60 * 100) / 100 : 0;
    },

    remainingHours() {
      return this.consultancyUsage ? 
        Math.round(this.consultancyUsage.remaining_minutes / 60 * 100) / 100 : 0;
    },

    usagePercentage() {
      if (!this.consultancyUsage || this.consultancyUsage.total_minutes_allocated === 0) return 0;
      return (this.consultancyUsage.used_minutes / this.consultancyUsage.total_minutes_allocated) * 100;
    }
  },

  watch: {
    activeTab(newTab) {
      if (newTab === 'available') {
        this.fetchAvailableSlots();
      } else if (newTab === 'bookings') {
        this.fetchMyBookings();
      }
    },

    filters: {
      handler() {
        if (this.activeTab === 'available') {
          this.fetchAvailableSlots();
        }
      },
      deep: true
    }
  },

  mounted() {
    this.fetchConsultancyUsage();
    this.fetchAvailableSlots();
  },

  methods: {
    async fetchConsultancyUsage() {
      try {
        const response = await ConsultancyService.user.getMyUsage();
        this.consultancyUsage = response.data?.data || response.data;
        console.log('Consultancy usage:', this.consultancyUsage);
      } catch (error) {
        console.error('Error fetching consultancy usage:', error);
        // Don't show error if user just doesn't have consultancy access
        if (error.message && !error.message.includes('404')) {
          this.$toast?.error('Failed to fetch consultancy usage: ' + error.message);
        }
        this.consultancyUsage = null;
      }
    },

    async fetchAvailableSlots() {
      if (!this.consultancyUsage) return;
      
      this.slotsLoading = true;
      try {
        const response = await ConsultancyService.user.getAvailableSlots(this.filters);
        this.availableSlots = response.data?.data || response.data || [];
        console.log('Available slots:', this.availableSlots);
      } catch (error) {
        console.error('Error fetching available slots:', error);
        this.$toast?.error('Failed to fetch available slots: ' + error.message);
        this.availableSlots = [];
      }
      this.slotsLoading = false;
    },

    async fetchMyBookings() {
      if (!this.consultancyUsage) return;
      
      this.bookingsLoading = true;
      try {
        const response = await ConsultancyService.user.getMyBookings();
        this.myBookings = response.data?.data || response.data || [];
        console.log('My bookings:', this.myBookings);
      } catch (error) {
        console.error('Error fetching bookings:', error);
        this.$toast?.error('Failed to fetch bookings: ' + error.message);
        this.myBookings = [];
      }
      this.bookingsLoading = false;
    },

    openBookingModal(slot) {
      this.selectedSlot = slot;
      this.bookingNotes = '';
      this.showBookingModal = true;
    },

    closeBookingModal() {
      this.showBookingModal = false;
      this.selectedSlot = null;
      this.bookingNotes = '';
    },

    async confirmBooking() {
      if (!this.selectedSlot) return;
      
      this.bookingLoading = true;
      try {
        await ConsultancyService.user.bookSession(this.selectedSlot._id, this.bookingNotes);
        this.$toast?.success('Session booked successfully!');
        
        this.closeBookingModal();
        
        // Refresh data
        await Promise.all([
          this.fetchConsultancyUsage(),
          this.fetchAvailableSlots(),
          this.fetchMyBookings()
        ]);
      } catch (error) {
        console.error('Error booking slot:', error);
        this.$toast?.error('Failed to book session: ' + error.message);
      }
      this.bookingLoading = false;
    },

    async cancelBookingWithConfirm(bookingId) {
      if (!confirm('Are you sure you want to cancel this booking?')) return;
      
      try {
        await ConsultancyService.user.cancelBooking(bookingId, 'Cancelled by user');
        this.$toast?.success('Booking cancelled successfully!');
        
        // Refresh data
        await Promise.all([
          this.fetchConsultancyUsage(),
          this.fetchMyBookings()
        ]);
      } catch (error) {
        console.error('Error cancelling booking:', error);
        this.$toast?.error('Failed to cancel booking: ' + error.message);
      }
    },

    // FIXED: Safe date formatting
    formatDate(dateString) {
      if (!dateString) return 'Invalid Date';
      
      try {
        let date;
        if (typeof dateString === 'string') {
          date = new Date(dateString);
        } else if (typeof dateString === 'object' && dateString.date) {
          date = new Date(dateString.date);
        } else {
          return 'Invalid Date';
        }

        if (isNaN(date.getTime())) return 'Invalid Date';

        return date.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      } catch (error) {
        console.error('Date formatting error:', error);
        return 'Invalid Date';
      }
    },

    // FIXED: Safe time formatting for slots
    formatSlotTime(slot) {
      try {
        if (!slot.start_time || !slot.end_time) return 'Invalid Time';

        let startStr = typeof slot.start_time === 'string' ? slot.start_time : slot.start_time?.date || '';
        let endStr = typeof slot.end_time === 'string' ? slot.end_time : slot.end_time?.date || '';

        const startDate = new Date(startStr);
        const endDate = new Date(endStr);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return 'Invalid Time';
        }

        const start = startDate.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });
        const end = endDate.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });

        return `${start} - ${end}`;
      } catch (error) {
        console.error('Time formatting error:', error);
        return 'Invalid Time';
      }
    },

    // FIXED: Safe time formatting for bookings
    formatBookingTime(booking) {
      try {
        const slot = booking.slot;
        if (!slot) return 'Invalid Time';
        
        return this.formatSlotTime(slot);
      } catch (error) {
        console.error('Booking time formatting error:', error);
        return 'Invalid Time';
      }
    },

    getStatusBadgeClass(status) {
      const statusClasses = {
        'pending': 'bg-warning',
        'confirmed': 'bg-success',
        'cancelled': 'bg-danger',
        'completed': 'bg-info'
      };
      return statusClasses[status] || 'bg-secondary';
    },

    canCancelBooking(booking) {
      if (!booking || !booking.status) return false;
      
      // Can cancel if status is pending or confirmed and the session hasn't started yet
      if (booking.status === 'cancelled' || booking.status === 'completed') {
        return false;
      }
      
      // Check if session is in the future (simple check)
      try {
        const slotDate = booking.slot?.date || booking.date;
        if (slotDate) {
          const sessionDate = new Date(slotDate);
          return sessionDate > new Date();
        }
      } catch (error) {
        console.error('Error checking cancellation eligibility:', error);
      }
      
      return true; // Default to allowing cancellation
    },

    goToPackages() {
      // Navigate to packages page
      this.$router.push('/packages');
    }
  }
};
</script>

<style scoped>
.consultancy-dashboard {
  padding: 20px;
}

.card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
}

.badge {
  font-size: 0.75em;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.progress {
  background-color: #e9ecef;
}

.alert {
  border: none;
  border-radius: 8px;
}

.modal {
  z-index: 1055;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
</style>