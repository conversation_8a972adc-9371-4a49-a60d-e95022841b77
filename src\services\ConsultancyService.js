// COMPLETELY FIXED ConsultancyService.js
import ApiService from '@/services/ApiService';
import ConsultancyApiRoutes from '@/ApiRoutes/consultancyApiRoutes';

// Helper function to handle responses consistently
const handleResponse = (res, resolve, reject, errorMessage) => {
    if (res && (res.status === 200 || res.data?.status === 200)) {
        resolve(res);
    } else {
        const error = res?.data?.error || res?.error || errorMessage;
        reject(new Error(error));
    }
};

const ConsultancyService = {
    // Admin Services
    admin: {
        // Slot Management - FIXED HTTP METHODS
        getAllSlots(filters = {}) {
            return new Promise((resolve, reject) => {
                // Use GET with query parameters instead of POST with body
                const queryParams = new URLSearchParams(filters).toString();
                const url = queryParams ? 
                    `${ConsultancyApiRoutes.Admin.GetAllSlots}?${queryParams}` : 
                    ConsultancyApiRoutes.Admin.GetAllSlots;
                
                ApiService.GET(url, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch slots")
                );
            });
        },

        createSlot(slotData) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.CreateSlot, slotData, (res) =>
                    handleResponse(res, resolve, reject, "Failed to create slot")
                );
            });
        },

        updateSlot(slotData) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateSlot, slotData, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update slot")
                );
            });
        },

        deleteSlot(slotId) {
            return new Promise((resolve, reject) => {
                ApiService.DELETE(ConsultancyApiRoutes.Admin.DeleteSlot(slotId), (res) =>
                    handleResponse(res, resolve, reject, "Failed to delete slot")
                );
            });
        },

        getSlot(slotId) {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetSlot(slotId), (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch slot")
                );
            });
        },

        // Booking Management - FIXED HTTP METHODS
        getAllBookings(filters = {}) {
            return new Promise((resolve, reject) => {
                // Use GET with query parameters instead of POST with body
                const queryParams = new URLSearchParams(filters).toString();
                const url = queryParams ? 
                    `${ConsultancyApiRoutes.Admin.GetAllBookings}?${queryParams}` : 
                    ConsultancyApiRoutes.Admin.GetAllBookings;
                
                ApiService.GET(url, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch bookings")
                );
            });
        },

        getBooking(bookingId) {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetBooking(bookingId), (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch booking")
                );
            });
        },

        updateBooking(bookingData) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateBooking, bookingData, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update booking")
                );
            });
        },

        cancelBooking(bookingId, reason = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.CancelBooking, { 
                    booking_id: bookingId, 
                    cancellation_reason: reason 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to cancel booking")
                );
            });
        },

        confirmBooking(bookingId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.ConfirmBooking, { 
                    booking_id: bookingId 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to confirm booking")
                );
            });
        },

        // Meeting Link Management
        updateMeetingLink(bookingId, meetingLink) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateMeetingLink, { 
                    booking_id: bookingId, 
                    meeting_link: meetingLink 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update meeting link")
                );
            });
        },

        sendMeetingLink(bookingId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.SendMeetingLink, { 
                    booking_id: bookingId 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to send meeting link")
                );
            });
        },

        // Statistics - FIXED TO USE GET
        getStats() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetStats, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch statistics")
                );
            });
        },

        // Reports
        getBookingReport(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetBookingReport, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch booking report")
                );
            });
        },

        getUsageReport(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetUsageReport, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch usage report")
                );
            });
        },

        // User Management
        getUserUsage(userId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetUserUsage, { user_id: userId }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch user usage")
                );
            });
        },

        updateUserUsage(userId, remainingMinutes, reason = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateUserUsage, { 
                    user_id: userId, 
                    remaining_minutes: remainingMinutes,
                    reason: reason
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update user usage")
                );
            });
        },

        initializeUserConsultancy(userId, packageId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.InitializeUserConsultancy, { 
                    user_id: userId, 
                    package_id: packageId 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to initialize user consultancy")
                );
            });
        },

        // Location services - FIXED TO USE GET
        getCountries() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetCountries, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch countries")
                );
            });
        },

        getCities(country) {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetCities(country), (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch cities")
                );
            });
        }
    },

    // User Services - THESE ARE CORRECT
    user: {
        // Get available slots for booking
        getAvailableSlots(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.GetAvailableSlots, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch available slots")
                );
            });
        },

        // Book a session
        bookSession(slotId, notes = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.BookSession, { 
                    slot_id: slotId, 
                    notes: notes 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to book session")
                );
            });
        },

        // Get user's bookings
        getMyBookings(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.GetMyBookings, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch bookings")
                );
            });
        },

        // Cancel booking
        cancelBooking(bookingId, reason = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.CancelBooking, { 
                    booking_id: bookingId, 
                    cancellation_reason: reason 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to cancel booking")
                );
            });
        },

        // Get consultancy usage - FIXED TO USE GET
        getMyUsage() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.User.GetMyUsage, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch usage data")
                );
            });
        },

        // Get upcoming sessions - FIXED TO USE GET
        getUpcomingSessions() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.User.GetUpcomingSessions, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch upcoming sessions")
                );
            });
        },

        // Validate booking before submission
        validateBooking(slotId) {
            return new Promise((resolve, reject) => {
                // Check user's consultancy usage first
                this.getMyUsage()
                    .then(usageResponse => {
                        const usage = usageResponse.data;
                        if (!usage) {
                            reject(new Error('No consultancy access. Please upgrade your package.'));
                            return;
                        }
                        
                        if (usage.remaining_minutes <= 0) {
                            reject(new Error('No consultancy hours remaining. Please upgrade your package.'));
                            return;
                        }
                        
                        // If validation passes, resolve with usage info
                        resolve(usage);
                    })
                    .catch(error => {
                        reject(error);
                    });
            });
        }
    }
};

export default ConsultancyService;