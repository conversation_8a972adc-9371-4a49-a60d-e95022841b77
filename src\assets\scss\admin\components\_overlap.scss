

// Typography
.text-theme{
    color: var(--mot-theme-color);
}
.text-justify{text-align: justify;}

// Table
.table.table-vertical-middle{
    th,td{
        vertical-align: middle;
    }
}

// Dropdown
.dropdown-menu{
    // Ensure dropdowns appear above modals
    z-index: 1080 !important;
    position: absolute !important;

    .router-link-exact-active{
        color: #ffffff!important;
        background-color: var(--mot-theme-color)!important;
        border-bottom: 0 solid var(--mot-theme-color)!important;
        @include transition(all 0.2s linear);
    }
}

// Ensure dropdown toggles work even when modals are open
.dropdown-toggle {
    pointer-events: auto !important;
    z-index: 1080 !important;
}

// Modal fixes to ensure header dropdowns work
.modal-backdrop {
    z-index: 1050 !important;
}

.modal {
    z-index: 1055 !important;
}

// Ensure header elements are always interactive even when modals are open
.header {
    pointer-events: auto !important;

    .navbar,
    .navbar-nav,
    .nav-item,
    .dropdown,
    .dropdown-menu,
    .profile {
        pointer-events: auto !important;
    }
}
