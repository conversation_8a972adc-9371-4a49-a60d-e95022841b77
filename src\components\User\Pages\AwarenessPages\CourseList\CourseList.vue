<template>
    <div class="course-list-page">
        <UserPageHeader
            title="Available Courses"
            subtitle="Choose from our comprehensive collection of AI awareness training courses"
            icon="fa fa-book"
            action-text="Back to Dashboard"
            action-icon="fa fa-arrow-left"
            :action-route="{ name: 'AwarenessEvaluation' }"
            background-color="linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)"
        />

        <CourseListStats :courses-count="availableCourseCount()" />

        <UserLoadingState :loading="loading" message="Loading Courses" color="#3b82f6" />

        <EmptyState
            v-if="!loading && courses.length === 0"
            title="No Courses Available"
            description="We're working on adding more courses to help you enhance your AI knowledge. Please check back soon!"
            icon="fa fa-graduation-cap"
            primary-color="#3b82f6"
            secondary-color="#1e40af"
            :primary-action="{ text: 'Back to Dashboard', icon: 'fa fa-arrow-left', route: { name: 'AwarenessEvaluation' } }"
        />

        <div v-if="!loading && courses.length > 0" class="courses-section">
            <div class="section-header">
                <h2 class="section-title">Start Your Learning Journey</h2>
                <div class="view-options">
                    <button @click="viewMode = 'grid'" :class="['view-btn', { active: viewMode === 'grid' }]"><i class="fa fa-th"></i></button>
                    <button @click="viewMode = 'list'" :class="['view-btn', { active: viewMode === 'list' }]"><i class="fa fa-list"></i></button>
                </div>
            </div>

            <div :class="['courses-container', viewMode]">
                <CourseListCard
                    v-for="(course, index) in courses"
                    :key="course._id"
                    :course="course"
                    :view-mode="viewMode"
                    :animation-delay="index * 0.1"
                    :start-course="startCourse"
                    :is-course-accessible="isCourseAccessible"
                />
            </div>
        </div>

        <CourseListBenefits />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import UserLoadingState from '@/components/General/UserLoadingState.vue';
import EmptyState from '@/components/General/EmptyState.vue';
import UserPageHeader from '@/components/General/UserPageHeader.vue';
import CourseListStats from './CourseListStats.vue';
import CourseListCard from './CourseListCard.vue';
import CourseListBenefits from './CourseListBenefits.vue';

const viewMode = ref('grid');
const { loading, courses, startCourse, getAvailableCourses, availableCourseCount, isCourseAccessible } = useAwarenessActions();

onMounted(getAvailableCourses);
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CourseList/CourseList.scss';
</style>
