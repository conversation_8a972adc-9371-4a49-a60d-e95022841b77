<template>
  <div class="consultancy-slots-management">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="h3 mb-1">Consultancy Slots Management</h2>
        <p class="text-muted mb-0">Manage available consultancy time slots</p>
      </div>
      <button
        @click="openCreateModal"
        class="btn btn-primary"
        :disabled="loading"
      >
        <i class="fa fa-plus me-2"></i>
        Create Slot
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Slots</h6>
                <h4 class="mb-0">{{ stats.total_slots || 0 }}</h4>
              </div>
              <div class="bg-primary bg-opacity-10 p-3 rounded">
                <i class="fa fa-calendar text-primary"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Active Slots</h6>
                <h4 class="mb-0">{{ stats.active_slots || 0 }}</h4>
              </div>
              <div class="bg-success bg-opacity-10 p-3 rounded">
                <i class="fa fa-clock text-success"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Today's Bookings</h6>
                <h4 class="mb-0">{{ stats.today_bookings || 0 }}</h4>
              </div>
              <div class="bg-warning bg-opacity-10 p-3 rounded">
                <i class="fa fa-users text-warning"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Upcoming</h6>
                <h4 class="mb-0">{{ stats.upcoming_bookings || 0 }}</h4>
              </div>
              <div class="bg-info bg-opacity-10 p-3 rounded">
                <i class="fa fa-eye text-info"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <select v-model="filters.type" class="form-select">
              <option value="">All Types</option>
              <option value="online">Online</option>
              <option value="in_person">In-Person</option>
            </select>
          </div>
          <div class="col-md-3">
            <input 
              v-model="filters.date" 
              type="date" 
              class="form-control"
              placeholder="Filter by date"
            >
          </div>
          <div class="col-md-3">
            <select v-model="filters.country" class="form-select">
              <option value="">All Countries</option>
              <option v-for="country in countries" :key="country" :value="country">
                {{ country }}
              </option>
            </select>
          </div>
          <div class="col-md-3">
            <select v-model="filters.is_active" class="form-select">
              <option value="">All Status</option>
              <option value="1">Active</option>
              <option value="0">Inactive</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2 text-muted">Loading slots...</p>
    </div>

    <!-- Slots Table -->
    <div v-else class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">Consultancy Slots ({{ slots.length }} found)</h5>
      </div>
      <div class="card-body p-0">
        <div v-if="slots.length === 0" class="text-center py-5">
          <i class="fa fa-calendar-times fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">No slots found</h5>
          <p class="text-muted mb-0">Create your first consultancy slot to get started.</p>
        </div>
        <div v-else class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Date & Time</th>
                <th>Type</th>
                <th>Location</th>
                <th>Capacity</th>
                <th>Status</th>
                <th>Bookings</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="slot in slots" :key="slot._id">
                <td>
                  <div class="fw-medium">{{ formatDate(slot.date) }}</div>
                  <small class="text-muted">{{ formatTime(slot) }}</small>
                </td>
                <td>
                  <span 
                    class="badge"
                    :class="slot.type === 'online' ? 'bg-primary' : 'bg-secondary'"
                  >
                    {{ slot.type === 'online' ? 'Online' : 'In-Person' }}
                  </span>
                </td>
                <td>
                  <div v-if="slot.type === 'online'">
                    <i class="fa fa-video text-muted me-1"></i>
                    Online Session
                  </div>
                  <div v-else>
                    <div class="fw-medium">{{ slot.city || 'N/A' }}, {{ slot.country || 'N/A' }}</div>
                    <small class="text-muted">{{ slot.location || 'No location specified' }}</small>
                  </div>
                </td>
                <td>
                  <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 60px; height: 6px;">
                      <div 
                        class="progress-bar"
                        :class="getProgressBarClass(slot)"
                        :style="{ width: getCapacityPercentage(slot) + '%' }"
                      ></div>
                    </div>
                    <small class="text-muted">
                      {{ slot.confirmed_bookings || 0 }}/{{ slot.max_capacity || 1 }}
                    </small>
                  </div>
                </td>
                <td>
                  <span 
                    class="badge"
                    :class="slot.is_active ? 'bg-success' : 'bg-warning'"
                  >
                    {{ slot.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td>
                  <span 
                    class="badge"
                    :class="slot.is_full ? 'bg-danger' : 'bg-success'"
                  >
                    {{ slot.is_full ? 'Full' : 'Available' }}
                  </span>
                </td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <button 
                      @click="editSlot(slot)"
                      class="btn btn-outline-primary"
                      :disabled="loading"
                    >
                      <i class="fa fa-edit"></i>
                    </button>
                    <button 
                      @click="viewSlot(slot)"
                      class="btn btn-outline-info"
                      :disabled="loading"
                    >
                      <i class="fa fa-eye"></i>
                    </button>
                    <button 
                      @click="deleteSlot(slot)"
                      class="btn btn-outline-danger"
                      :disabled="loading || (slot.confirmed_bookings > 0)"
                      :title="(slot.confirmed_bookings > 0) ? 'Cannot delete slot with bookings' : 'Delete slot'"
                    >
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div 
      v-if="showCreateModal" 
      class="modal fade show d-block" 
      tabindex="-1"
      style="background: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ editingSlot ? 'Edit Slot' : 'Create New Slot' }}
            </h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="closeModal"
              :disabled="loading"
            ></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveSlot">
              <div class="row g-3">
                <!-- Date and Time -->
                <div class="col-md-6">
                  <label class="form-label">Date *</label>
                  <input 
                    v-model="newSlot.date" 
                    type="date" 
                    class="form-control"
                    required
                    :min="today"
                  >
                </div>
                <div class="col-md-3">
                  <label class="form-label">Start Time *</label>
                  <input 
                    v-model="newSlot.start_time" 
                    type="time" 
                    class="form-control"
                    required
                  >
                </div>
                <div class="col-md-3">
                  <label class="form-label">End Time *</label>
                  <input 
                    v-model="newSlot.end_time" 
                    type="time" 
                    class="form-control"
                    required
                  >
                </div>

                <!-- Type -->
                <div class="col-md-6">
                  <label class="form-label">Session Type *</label>
                  <select 
                    v-model="newSlot.type" 
                    class="form-select"
                    required
                  >
                    <option value="online">Online</option>
                    <option value="in_person">In-Person</option>
                  </select>
                </div>

                <!-- Capacity -->
                <div class="col-md-6">
                  <label class="form-label">Max Capacity *</label>
                  <input 
                    v-model.number="newSlot.max_capacity" 
                    type="number" 
                    class="form-control"
                    min="1"
                    max="50"
                    required
                  >
                </div>

                <!-- Location Fields (for in-person) -->
                <template v-if="newSlot.type === 'in_person'">
                  <div class="col-md-6">
                    <label class="form-label">Country *</label>
                    <select 
                      v-model="newSlot.country" 
                      class="form-select"
                      required
                    >
                      <option value="">Select Country</option>
                      <option v-for="country in countries" :key="country" :value="country">
                        {{ country }}
                      </option>
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">City</label>
                    <input 
                      v-model="newSlot.city" 
                      type="text" 
                      class="form-control"
                      placeholder="Enter city"
                    >
                  </div>
                  <div class="col-12">
                    <label class="form-label">Location/Venue</label>
                    <input 
                      v-model="newSlot.location" 
                      type="text" 
                      class="form-control"
                      placeholder="Enter venue name"
                    >
                  </div>
                  <div class="col-12">
                    <label class="form-label">Full Address</label>
                    <textarea 
                      v-model="newSlot.address" 
                      class="form-control"
                      rows="2"
                      placeholder="Complete address with building, floor, etc."
                    ></textarea>
                  </div>
                </template>

                <!-- Online Meeting Link Template -->
                <template v-if="newSlot.type === 'online'">
                  <div class="col-12">
                    <label class="form-label">Meeting Link Template</label>
                    <input 
                      v-model="newSlot.meeting_link_template" 
                      type="url" 
                      class="form-control"
                      placeholder="https://zoom.us/j/..."
                    >
                    <small class="form-text text-muted">
                      Default meeting link for this slot (can be customized per booking)
                    </small>
                  </div>
                </template>

                <!-- Notes -->
                <div class="col-12">
                  <label class="form-label">Notes</label>
                  <textarea 
                    v-model="newSlot.notes" 
                    class="form-control"
                    rows="3"
                    placeholder="Additional notes or instructions..."
                  ></textarea>
                </div>

                <!-- Active Status -->
                <div class="col-12">
                  <div class="form-check">
                    <input 
                      v-model="newSlot.is_active" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="isActive"
                    >
                    <label class="form-check-label" for="isActive">
                      Active (available for booking)
                    </label>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="closeModal"
              :disabled="loading"
            >
              Cancel
            </button>
            <button 
              type="button" 
              class="btn btn-primary" 
              @click="saveSlot"
              :disabled="loading || !isFormValid"
            >
              <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
              {{ loading ? 'Saving...' : (editingSlot ? 'Update Slot' : 'Create Slot') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ConsultancyService from '@/services/ConsultancyService';

export default {
  name: 'AdminConsultancySlots',
  data() {
    return {
      slots: [],
      loading: false,
      showCreateModal: false,
      editingSlot: null,
      
      filters: {
        type: '',
        date: '',
        country: '',
        is_active: ''
      },
      
      newSlot: {
        date: '',
        start_time: '',
        end_time: '',
        type: 'online',
        location: '',
        country: '',
        city: '',
        address: '',
        max_capacity: 1,
        is_active: true,
        notes: '',
        meeting_link_template: ''
      },

      countries: [
        'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 
        'France', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Switzerland',
        'Austria', 'Belgium', 'Italy', 'Spain', 'Portugal', 'Ireland'
      ],

      stats: {
        total_slots: 0,
        active_slots: 0,
        today_bookings: 0,
        upcoming_bookings: 0
      }
    };
  },

  computed: {
    today() {
      return new Date().toISOString().split('T')[0];
    },

    isFormValid() {
      return this.newSlot.date && 
             this.newSlot.start_time && 
             this.newSlot.end_time && 
             this.newSlot.max_capacity > 0 &&
             (this.newSlot.type === 'online' || this.newSlot.country);
    }
  },

  watch: {
    filters: {
      handler() {
        this.fetchSlots();
      },
      deep: true
    },
    
    'newSlot.type'() {
      // Reset location fields when type changes
      if (this.newSlot.type === 'online') {
        this.newSlot.location = '';
        this.newSlot.country = '';
        this.newSlot.city = '';
        this.newSlot.address = '';
      } else {
        this.newSlot.meeting_link_template = '';
      }
    }
  },

  mounted() {
    this.fetchSlots();
    this.fetchStats();
  },

  methods: {
    async fetchSlots() {
      this.loading = true;
      try {
        const response = await ConsultancyService.admin.getAllSlots(this.filters);
        
        // Handle different response structures
        if (response.data?.data?.data) {
          // Paginated response
          this.slots = response.data.data.data;
        } else if (response.data?.data) {
          // Direct data response or array
          this.slots = Array.isArray(response.data.data) ? response.data.data : [];
        } else {
          this.slots = [];
        }

        console.log('Fetched slots:', this.slots);
      } catch (error) {
        console.error('Error fetching slots:', error);
        this.$toast?.error('Failed to fetch slots: ' + error.message);
        this.slots = [];
      }
      this.loading = false;
    },

    async fetchStats() {
      try {
        const response = await ConsultancyService.admin.getStats();
        this.stats = response.data?.data || response.data || {};
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Don't show error for stats as it's not critical
      }
    },

    openCreateModal() {
      this.editingSlot = null;
      this.resetForm();
      this.showCreateModal = true;
    },

    editSlot(slot) {
      this.editingSlot = slot;
      this.populateForm(slot);
      this.showCreateModal = true;
    },

    viewSlot(slot) {
      // Show slot details
      alert(`Slot Details:\nDate: ${this.formatDate(slot.date)}\nTime: ${this.formatTime(slot)}\nType: ${slot.type}\nCapacity: ${slot.max_capacity}`);
    },

    async deleteSlot(slot) {
      if (!confirm('Are you sure you want to delete this slot?')) {
        return;
      }

      if ((slot.confirmed_bookings || 0) > 0) {
        this.$toast?.error('Cannot delete slot with confirmed bookings');
        return;
      }

      try {
        await ConsultancyService.admin.deleteSlot(slot._id);
        this.$toast?.success('Slot deleted successfully!');
        this.fetchSlots();
        this.fetchStats();
      } catch (error) {
        console.error('Error deleting slot:', error);
        this.$toast?.error('Failed to delete slot: ' + error.message);
      }
    },

async saveSlot() {
  if (!this.isFormValid) {
    this.$toast?.error('Please fill in all required fields');
    return;
  }

  this.loading = true;
  try {
    // Prepare slot data
    const slotData = {
      date: this.newSlot.date,
      start_time: this.newSlot.start_time,
      end_time: this.newSlot.end_time,
      type: this.newSlot.type,
      max_capacity: parseInt(this.newSlot.max_capacity) || 1,
      is_active: Boolean(this.newSlot.is_active),
      notes: this.newSlot.notes || '',
    };

    // Add location fields based on type
    if (this.newSlot.type === 'in_person') {
      slotData.country = this.newSlot.country || '';
      slotData.city = this.newSlot.city || '';
      slotData.location = this.newSlot.location || '';
      slotData.address = this.newSlot.address || '';
      // Clear online fields
      slotData.meeting_link_template = '';
    } else {
      slotData.meeting_link_template = this.newSlot.meeting_link_template || '';
      // Clear location fields
      slotData.country = '';
      slotData.city = '';
      slotData.location = '';
      slotData.address = '';
    }

    console.log('Sending slot data:', slotData);

    if (this.editingSlot) {
      // Update existing slot
      slotData.id = this.editingSlot._id;
      console.log('Updating slot with ID:', slotData.id);
      
      const response = await ConsultancyService.admin.updateSlot(slotData);
      console.log('Update response:', response);
      
      this.$toast?.success('Slot updated successfully!');
    } else {
      // Create new slot
      console.log('Creating new slot');
      
      const response = await ConsultancyService.admin.createSlot(slotData);
      console.log('Create response:', response);
      
      this.$toast?.success('Slot created successfully!');
    }

    this.closeModal();
    await this.fetchSlots();
    await this.fetchStats();

  } catch (error) {
    console.error('Error saving slot:', error);
    
    // Handle different error types
    if (error.response && error.response.status === 422) {
      const validationErrors = error.response.data?.errors || {};
      const errorMessages = Object.values(validationErrors).flat();
      this.$toast?.error('Validation Error: ' + errorMessages.join(', '));
      console.log('Validation errors:', validationErrors);
      console.log('Sent data:', error.response.data?.data);
    } else {
      this.$toast?.error('Failed to save slot: ' + (error.message || 'Unknown error'));
    }
  } finally {
    this.loading = false;
  }
},

    closeModal() {
      this.showCreateModal = false;
      this.editingSlot = null;
      this.resetForm();
    },

    resetForm() {
      this.newSlot = {
        date: '',
        start_time: '',
        end_time: '',
        type: 'online',
        location: '',
        country: '',
        city: '',
        address: '',
        max_capacity: 1,
        is_active: true,
        notes: '',
        meeting_link_template: ''
      };
    },

    // FIXED: Safe date parsing for populateForm
    populateForm(slot) {
      try {
        // Safely extract date
        let date = '';
        if (slot.date) {
          if (typeof slot.date === 'string') {
            date = slot.date.split('T')[0]; // Extract just the date part
          } else if (typeof slot.date === 'object' && slot.date.date) {
            date = slot.date.date.split('T')[0];
          } else {
            date = this.today; // fallback to today
          }
        }

        // Safely extract start time
        let startTime = '';
        if (slot.start_time) {
          if (typeof slot.start_time === 'string') {
            const dateObj = new Date(slot.start_time);
            if (!isNaN(dateObj.getTime())) {
              startTime = dateObj.toTimeString().slice(0, 5);
            }
          }
        }
        if (!startTime) startTime = '09:00'; // default

        // Safely extract end time
        let endTime = '';
        if (slot.end_time) {
          if (typeof slot.end_time === 'string') {
            const dateObj = new Date(slot.end_time);
            if (!isNaN(dateObj.getTime())) {
              endTime = dateObj.toTimeString().slice(0, 5);
            }
          }
        }
        if (!endTime) endTime = '10:00'; // default

        this.newSlot = {
          date: date,
          start_time: startTime,
          end_time: endTime,
          type: slot.type || 'online',
          location: slot.location || '',
          country: slot.country || '',
          city: slot.city || '',
          address: slot.address || '',
          max_capacity: slot.max_capacity || 1,
          is_active: slot.is_active !== false,
          notes: slot.notes || '',
          meeting_link_template: slot.meeting_link_template || ''
        };
      } catch (error) {
        console.error('Error populating form:', error);
        this.$toast?.error('Error loading slot data');
        this.resetForm();
      }
    },

    // FIXED: Safe date formatting
    formatDate(date) {
      try {
        if (!date) return 'Invalid Date';
        
        let dateStr = '';
        if (typeof date === 'string') {
          dateStr = date;
        } else if (typeof date === 'object' && date.date) {
          dateStr = date.date;
        } else {
          return 'Invalid Date';
        }

        const dateObj = new Date(dateStr);
        if (isNaN(dateObj.getTime())) {
          return 'Invalid Date';
        }

        return dateObj.toLocaleDateString('en-US', {
          weekday: 'short',
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } catch (error) {
        console.error('Date formatting error:', error);
        return 'Invalid Date';
      }
    },

    // FIXED: Safe time formatting
    formatTime(slot) {
      try {
        if (!slot.start_time || !slot.end_time) return 'Invalid Time';

        let startStr = typeof slot.start_time === 'string' ? slot.start_time : slot.start_time?.date || '';
        let endStr = typeof slot.end_time === 'string' ? slot.end_time : slot.end_time?.date || '';

        const startDate = new Date(startStr);
        const endDate = new Date(endStr);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return 'Invalid Time';
        }

        const start = startDate.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });
        const end = endDate.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });

        return `${start} - ${end}`;
      } catch (error) {
        console.error('Time formatting error:', error);
        return 'Invalid Time';
      }
    },

    getCapacityPercentage(slot) {
      if (!slot.max_capacity) return 0;
      return Math.round(((slot.confirmed_bookings || 0) / slot.max_capacity) * 100);
    },

    getProgressBarClass(slot) {
      const percentage = this.getCapacityPercentage(slot);
      if (percentage >= 100) return 'bg-danger';
      if (percentage >= 80) return 'bg-warning';
      return 'bg-success';
    }
  }
};
</script>

<style scoped>
.consultancy-slots-management {
  padding: 1rem;
}

.card {
  transition: transform 0.2s ease-in-out;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
}

.btn-outline-primary,
.btn-outline-success,
.btn-outline-info,
.btn-outline-danger {
  border-width: 1px;
}

.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}

.progress {
  background-color: #e9ecef;
}

.modal {
  z-index: 1055;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
</style>