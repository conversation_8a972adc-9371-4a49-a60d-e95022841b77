// COMPLETELY FIXED consultancyApiRoutes.js
const BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

const ConsultancyApiRoutes = {
    // Admin Routes - FIXED TO MATCH BACKEND
    Admin: {
        // Slot Management - CORRECTED HTTP METHODS
        GetAllSlots: BASE_URL + "/api/secure/admin/consultancy/slots",
        CreateSlot: BASE_URL + "/api/secure/admin/consultancy/slots/create", 
        UpdateSlot: BASE_URL + "/api/secure/admin/consultancy/slots/update",
        DeleteSlot: (id) => `${BASE_URL}/api/secure/admin/consultancy/slots/delete/${id}`,
        GetSlot: (id) => `${BASE_URL}/api/secure/admin/consultancy/slots/${id}`,
        
        // Booking Management - CORRECTED HTTP METHODS
        GetAllBookings: BASE_URL + "/api/secure/admin/consultancy/bookings",
        GetBooking: (id) => `${BASE_URL}/api/secure/admin/consultancy/bookings/${id}`,
        UpdateBooking: BASE_URL + "/api/secure/admin/consultancy/bookings/update",
        CancelBooking: BASE_URL + "/api/secure/admin/consultancy/bookings/cancel",
        ConfirmBooking: BASE_URL + "/api/secure/admin/consultancy/bookings/confirm",
        
        // Meeting Link Management
        UpdateMeetingLink: BASE_URL + "/api/secure/admin/consultancy/bookings/meeting-link",
        SendMeetingLink: BASE_URL + "/api/secure/admin/consultancy/bookings/send-link",
        
        // Statistics and Reports
        GetStats: BASE_URL + "/api/secure/admin/consultancy/stats",
        GetBookingReport: BASE_URL + "/api/secure/admin/consultancy/reports/bookings", 
        GetUsageReport: BASE_URL + "/api/secure/admin/consultancy/reports/usage",
        
        // User Consultancy Management
        GetUserUsage: BASE_URL + "/api/secure/admin/consultancy/users/usage",
        UpdateUserUsage: BASE_URL + "/api/secure/admin/consultancy/users/usage/update",
        InitializeUserConsultancy: BASE_URL + "/api/secure/admin/consultancy/users/initialize",
        
        // Location Management
        GetCountries: BASE_URL + "/api/secure/admin/consultancy/locations/countries",
        GetCities: (country) => `${BASE_URL}/api/secure/admin/consultancy/locations/cities/${country}`
    },

    // User Routes - CORRECT
    User: {
        GetAvailableSlots: BASE_URL + "/api/secure/user/consultancy/slots/available",
        BookSession: BASE_URL + "/api/secure/user/consultancy/book", 
        GetMyBookings: BASE_URL + "/api/secure/user/consultancy/my-bookings",
        CancelBooking: BASE_URL + "/api/secure/user/consultancy/cancel",
        GetMyUsage: BASE_URL + "/api/secure/user/consultancy/usage",
        GetUpcomingSessions: BASE_URL + "/api/secure/user/consultancy/upcoming"
    }
};

export default ConsultancyApiRoutes;